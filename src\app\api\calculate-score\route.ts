import { NextRequest, NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface CalculateScoreRequest {
  userId: string
  gameCampaignId: string
  levelNumber: number
  timeCompleted: number
}

interface CalculateScoreResponse {
  image: string
  rewardKey: number
  segment: number
  score: number
  title: {
    en: string
    id: string
    ko: string
    th: string
    vi: string
  }
}

export async function POST(req: NextRequest) {
  try {
    const body: CalculateScoreRequest = await req.json()
    console.log('Calculate Score API - Incoming Request:', body)

    // Validate required fields
    if (
      !body.userId ||
      !body.gameCampaignId ||
      typeof body.levelNumber !== 'number' ||
      typeof body.timeCompleted !== 'number'
    ) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, gameCampaignId, levelNumber, timeCompleted' },
        { status: 400 }
      )
    }

    // Verify that the user has passed this level (anti-cheat check)
    const hasPassedLevel = UserLevelManager.hasUserPassedLevel(body.userId, body.levelNumber)
    if (!hasPassedLevel) {
      console.warn(`User ${body.userId} attempted to calculate score for level ${body.levelNumber} without passing it`)
      return NextResponse.json({ error: 'User has not passed this level' }, { status: 403 })
    }

    // Call the external calculate-score API with headers
    const { data } = await axios.post<CalculateScoreResponse>(
      'http://192.168.88.71/api/v5/event-vn/calculate-score',
      body,
      {
        headers: {
          'Content-Type': 'application/json',
          accessKey: '****************************************************************',
          Authorization:
            'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngzOTFkNmI0ZWNhZGI2NjI1YWY3MTZmZTY3NWNjMjFjZiIsImV4cCI6MTc1OTEyMTYyOX0.PGISaysSJoK5P0EojUWbcI4NurLtmNUorJmpKkSvFvA'
        }
      }
    )

    console.log('Calculate Score API - External API Response:', data)
    return NextResponse.json(data)
  } catch (error) {
    const err = error as AxiosError
    console.error('Calculate Score API error:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    })

    return NextResponse.json(
      { error: err.message || 'Failed to calculate score' },
      { status: err.response?.status ?? 500 }
    )
  }
}
