import { NextRequest, NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'
import { UserLevelManager } from '@/lib/userLevelStorage'

interface UsePresentUnboxingRequest {
  gameCampaignId: string
  from: string
  levelNumber: number
  timeCompleted: number
  userId: string // Added userId for our internal tracking
}

interface UsePresentUnboxingResponse {
  image: string
  rewardKey: number
  segment: number
  score: number
  title: {
    en: string
    id: string
    ko: string
    th: string
    vi: string
  }
}

export async function POST(req: NextRequest) {
  try {
    const body: UsePresentUnboxingRequest = await req.json()
    console.log('Use Present Unboxing API - Incoming Request:', body)

    // Validate required fields
    if (
      !body.gameCampaignId ||
      !body.from ||
      typeof body.levelNumber !== 'number' ||
      typeof body.timeCompleted !== 'number' ||
      !body.userId
    ) {
      return NextResponse.json(
        { error: 'Missing required fields: gameCampaignId, from, levelNumber, timeCompleted, userId' },
        { status: 400 }
      )
    }

    // Update our internal level tracking - mark user as completing this level
    UserLevelManager.updateUserLevel(body.userId, body.levelNumber, true)

    // Also update their current level to the next level if they're progressing
    UserLevelManager.updateUserLevel(body.userId, body.levelNumber + 1, false)

    console.log('Updated user level tracking for userId:', body.userId, 'level:', body.levelNumber)

    // Prepare request for external API (include userId as it's required by external API)
    const externalApiPayload = {
      gameCampaignId: body.gameCampaignId,
      from: body.from,
      levelNumber: body.levelNumber,
      timeCompleted: body.timeCompleted,
      userId: body.userId
    }

    // Call the external BE API
    // Call the external BE API
    const { data } = await axios.post<UsePresentUnboxingResponse>(
      'http://*************/api/v5/event-vn/use-present-unboxing',
      externalApiPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          accessKey: '****************************************************************',
          Authorization:
            'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngzOTFkNmI0ZWNhZGI2NjI1YWY3MTZmZTY3NWNjMjFjZiIsImV4cCI6MTc1OTEyMTYyOX0.PGISaysSJoK5P0EojUWbcI4NurLtmNUorJmpKkSvFvA'
        }
      }
    )

    console.log('Use Present Unboxing API - External API Response:', data)
    return NextResponse.json(data)
  } catch (error) {
    const err = error as AxiosError
    console.error('Use Present Unboxing API error:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    })

    return NextResponse.json(
      { error: err.message || 'Failed to process unboxing request' },
      { status: err.response?.status ?? 500 }
    )
  }
}
