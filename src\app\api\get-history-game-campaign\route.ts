import { NextRequest, NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'

interface GetHistoryGameCampaignRequest {
  userId: string
  gameCampaignId: string
}

export interface HistoryEntry {
  createdAt: string
  icon?: string
  image?: string
  level?: number
  navigateTo?: string
  title: {
    en: string
    id: string
    ko: string
    ms: string
    th: string
    vi: string
  }
}

export type GetHistoryGameCampaignResponse = HistoryEntry[]

export async function POST(req: NextRequest) {
  try {
    const body: GetHistoryGameCampaignRequest = await req.json()
    console.log('Get History Game Campaign API - Incoming Request:', body)

    // Validate required fields
    if (!body.userId || !body.gameCampaignId) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, gameCampaignId' },
        { status: 400 }
      )
    }

    const { data } = await axios.post<GetHistoryGameCampaignResponse>(
      'http://192.168.88.71/api/v5/api-asker-vn/get-history-game-campaign',
      body,
      {
        headers: {
          accessKey: '****************************************************************',
          Authorization:
            'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngzOTFkNmI0ZWNhZGI2NjI1YWY3MTZmZTY3NWNjMjFjZiIsImV4cCI6MTc1OTEyMTYyOX0.PGISaysSJoK5P0EojUWbcI4NurLtmNUorJmpKkSvFvA',
          'Content-Type': 'application/json'
        }
      }
    )

    console.log('Get History Game Campaign API - Outgoing Response Data:', data)
    return NextResponse.json(data)
  } catch (error) {
    const err = error as AxiosError
    console.error('Error while handling get history game campaign request:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    })

    return NextResponse.json({ error: err.message }, { status: 500 })
  }
}
