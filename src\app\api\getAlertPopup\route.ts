// src/app/api/getAlertPopup/route.ts
import { NextResponse } from 'next/server'
import axios from 'axios'

export async function POST(req: Request) {
  try {
    const body = await req.json()
    console.log('Incoming Request Body:', body)

    const { data } = await axios.post('http://192.168.88.71/api/v5/api-asker-vn/get-alert-popup', body, {
      headers: {
        accessKey: '****************************************************************',
        Authorization:
          'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngzOTFkNmI0ZWNhZGI2NjI1YWY3MTZmZTY3NWNjMjFjZiIsImV4cCI6MTc1OTEyMTYyOX0.PGISaysSJoK5P0EojUWbcI4NurLtmNUorJmpKkSvFvA',
        'Content-Type': 'application/json'
      }
    })

    console.log('Outgoing Response Data:', data)
    return NextResponse.json(data)
  } catch (err: unknown) {
    if (axios.isAxiosError(err)) {
      console.error('Axios error:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      })
      return NextResponse.json({ error: err.message }, { status: err.response?.status ?? 500 })
    }

    console.error('Unknown error:', err)
    return NextResponse.json({ error: 'Request failed' }, { status: 500 })
  }
}
