# API Proxy System with Anti-Cheat Verification

This document describes the API proxy system implemented for the mini-game with anti-cheat verification.

## Overview

The system consists of several components:
1. **In-memory user level storage** for anti-cheat verification
2. **Verification APIs** for the backend to validate user progress
3. **Present unboxing API proxy** that handles rewards
4. **TanStack Query integration** for frontend API calls
5. **Game store integration** for level tracking

## API Endpoints

### 1. `/api/win-mini-game` (POST)
**Purpose**: Verify if a user is currently winning on a specific level.

**Request**:
```json
{
  "accessKey": "abcdefg",
  "userId": "string",
  "level": number
}
```

**Response**:
```json
{
  "isWinning": boolean
}
```

**Usage**: Called by the backend to verify if a user is legitimately on the current level.

### 2. `/api/verify-passed-level` (POST)
**Purpose**: Verify if a user has previously passed a specific level.

**Request**:
```json
{
  "accessKey": "abcdefg", 
  "userId": "string",
  "level": number
}
```

**Response**:
```json
{
  "isWinning": boolean
}
```

**Usage**: Called by the backend to verify if a user has completed a level in the past.

### 3. `/api/use-present-unboxing` (POST)
**Purpose**: Proxy to the external unboxing API with internal level tracking.

**Request**:
```json
{
  "gameCampaignId": "string",
  "from": "WEBVIEW",
  "levelNumber": number,
  "timeCompleted": number,
  "userId": "string"
}
```

**Response**:
```json
{
  "image": "string",
  "rewardKey": number,
  "segment": number,
  "title": {
    "en": "string",
    "id": "string", 
    "ko": "string",
    "th": "string",
    "vi": "string"
  }
}
```

**Usage**: Called when user wins a level. Updates internal tracking and calls external API.

## Frontend Integration

### Hook Usage
```typescript
import { usePresentUnboxing } from '@/hooks/usePresentUnboxing'

const { mutateAsync: presentUnboxing } = usePresentUnboxing()

// Call when user wins
const reward = await presentUnboxing({
  gameCampaignId: 'campaign-id',
  from: 'WEBVIEW',
  levelNumber: 5,
  timeCompleted: 120, // seconds remaining
  userId: 'user-id'
})
```

### Game Store Integration
The game store automatically:
- Initializes user level tracking when campaign loads
- Updates level progress when users complete levels
- Integrates with the anti-cheat system

## Anti-Cheat Flow

1. **User loads game**: Level data is initialized in memory storage
2. **User wins level**: Frontend calls `/api/use-present-unboxing`
3. **Internal tracking**: Our system updates user's level progress
4. **External API call**: System calls backend unboxing API
5. **Backend verification**: Backend calls our verification APIs
6. **Validation**: Our APIs check stored user data
7. **Response**: Backend returns reward if validation passes

## Security Features

- **Access key validation**: All verification APIs require correct access key
- **In-memory storage**: User progress tracked server-side
- **Time validation**: Time remaining is passed for additional verification
- **Level progression**: System ensures users can only progress sequentially

## Error Handling

- APIs gracefully handle missing users (return `isWinning: false`)
- Frontend falls back to static reward data if unboxing API fails
- Comprehensive error logging for debugging

## Testing

To test the system:
1. Start the development server
2. Play through levels in the game
3. Check browser console for API calls and responses
4. Verify reward data is displayed correctly in win dialog

## Notes

- The current implementation uses in-memory storage
- For production, consider replacing with a proper database
- User ID is currently hardcoded as 'x391d6b4ecadb6625af716fe675cc21cf'
- The system is designed to be easily extensible for additional anti-cheat measures
